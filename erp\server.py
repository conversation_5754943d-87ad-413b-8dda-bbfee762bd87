"""
FastAPI ASGI Server for ERP system
"""
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional, List
import json

from .config import config
from .database.registry import DatabaseRegistry
from .addons.loader import <PERSON>don<PERSON>oader
from .models.base import ModelRegistry
from .utils import (
    APIResponse, ModelResponse, handle_database_error, handle_generic_error,
    ModelRequestHandler, RequestValidator, AuthenticationHandler,
    timing_middleware, error_handling_middleware, logging_middleware, database_middleware,
    environment_middleware
)


class ERPServer:
    """Main ERP server application"""
    
    def __init__(self):
        self.addon_loader = AddonLoader()
        self.app = self._create_app()
        self._setup_middleware()
        self._setup_routes()
    
    def _create_app(self) -> FastAPI:
        """Create FastAPI application with lifespan management"""
        
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # Startup
            print("Starting ERP server...")
            await self._load_addons()
            yield
            # Shutdown
            print("Shutting down ERP server...")
            await DatabaseRegistry.close_all()
        
        return FastAPI(
            title="ERP System",
            description="Odoo-like ERP system with async support",
            version="1.0.0",
            lifespan=lifespan
        )
    
    def _setup_middleware(self):
        """Setup FastAPI middleware"""
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=config.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Custom middleware (order matters - database_middleware must come before environment_middleware)
        self.app.middleware("http")(timing_middleware)
        self.app.middleware("http")(error_handling_middleware)
        self.app.middleware("http")(logging_middleware)
        self.app.middleware("http")(environment_middleware)
        self.app.middleware("http")(database_middleware)
    
    async def _load_addons(self):
        """Load all addons"""
        try:
            await self.addon_loader.discover_addons()
            await self.addon_loader.load_addons()
            print(f"Loaded {len(self.addon_loader.loaded_addons)} addons")
        except Exception as e:
            print(f"Error loading addons: {e}")
            raise
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.get("/")
        async def root():
            return APIResponse.success({"message": "ERP System API", "version": "1.0.0"})
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            try:
                # Check database connection
                db = await DatabaseRegistry.get_current_database()
                if db:
                    await db.execute("SELECT 1")
                    db_status = "connected"
                else:
                    db_status = "no_connection"
                
                return APIResponse.success({
                    "status": "healthy",
                    "database": db_status,
                    "addons": len(self.addon_loader.loaded_addons)
                })
            except Exception as e:
                return APIResponse.error(f"Health check failed: {str(e)}", status_code=503)
        
        @self.app.get("/addons")
        async def list_addons():
            """List all loaded addons"""
            addons_info = {}
            for name, manifest in self.addon_loader.loaded_addons.items():
                addons_info[name] = {
                    "name": manifest.name,
                    "version": manifest.version,
                    "description": manifest.description,
                    "depends": manifest.depends,
                    "installable": manifest.installable
                }
            return APIResponse.success(addons_info)
        
        @self.app.get("/models")
        async def list_models():
            """List all registered models"""
            models_info = {}
            for name, model_class in ModelRegistry.all().items():
                models_info[name] = {
                    "name": name,
                    "description": getattr(model_class, '_description', ''),
                    "table": getattr(model_class, '_table', ''),
                    "fields": list(getattr(model_class, '_fields', {}).keys())
                }
            return APIResponse.success(models_info)
        
        # Model CRUD endpoints
        @self.app.post("/api/{model_name}")
        async def create_record(model_name: str, request: Request):
            """Create a new record"""
            try:
                data = await request.json()
                handler = ModelRequestHandler(model_name)
                record = await handler.create(data)
                return ModelResponse.success(record)
            except Exception as e:
                return handle_database_error(e)
        
        @self.app.get("/api/{model_name}")
        async def search_records(model_name: str, request: Request):
            """Search records with domain and options"""
            try:
                # Parse query parameters
                domain = request.query_params.get('domain', '[]')
                limit = int(request.query_params.get('limit', 100))
                offset = int(request.query_params.get('offset', 0))
                order = request.query_params.get('order', 'id')
                
                # Parse domain from JSON string
                if isinstance(domain, str):
                    domain = json.loads(domain)
                
                handler = ModelRequestHandler(model_name)
                records = await handler.search(domain, limit=limit, offset=offset, order=order)
                return ModelResponse.success(records)
            except Exception as e:
                return handle_database_error(e)
        
        @self.app.get("/api/{model_name}/{record_id}")
        async def read_record(model_name: str, record_id: str):
            """Read a specific record"""
            try:
                handler = ModelRequestHandler(model_name)
                record = await handler.read(record_id)
                if record:
                    return ModelResponse.success(record)
                else:
                    raise HTTPException(status_code=404, detail="Record not found")
            except HTTPException:
                raise
            except Exception as e:
                return handle_database_error(e)
        
        @self.app.put("/api/{model_name}/{record_id}")
        async def update_record(model_name: str, record_id: str, request: Request):
            """Update a specific record"""
            try:
                data = await request.json()
                handler = ModelRequestHandler(model_name)
                record = await handler.update(record_id, data)
                if record:
                    return ModelResponse.success(record)
                else:
                    raise HTTPException(status_code=404, detail="Record not found")
            except HTTPException:
                raise
            except Exception as e:
                return handle_database_error(e)
        
        @self.app.delete("/api/{model_name}/{record_id}")
        async def delete_record(model_name: str, record_id: str):
            """Delete a specific record"""
            try:
                handler = ModelRequestHandler(model_name)
                success = await handler.delete(record_id)
                if success:
                    return ModelResponse.success({"deleted": True})
                else:
                    raise HTTPException(status_code=404, detail="Record not found")
            except HTTPException:
                raise
            except Exception as e:
                return handle_database_error(e)
        
        # Batch operations
        @self.app.post("/api/{model_name}/batch")
        async def batch_operations(model_name: str, request: Request):
            """Perform batch operations on records"""
            try:
                data = await request.json()
                operation = data.get('operation')
                records_data = data.get('records', [])
                
                handler = ModelRequestHandler(model_name)
                
                if operation == 'create':
                    results = []
                    for record_data in records_data:
                        record = await handler.create(record_data)
                        results.append(record)
                    return ModelResponse.success(results)
                
                elif operation == 'update':
                    results = []
                    for record_data in records_data:
                        record_id = record_data.pop('id', None)
                        if record_id:
                            record = await handler.update(record_id, record_data)
                            results.append(record)
                    return ModelResponse.success(results)
                
                elif operation == 'delete':
                    results = []
                    for record_data in records_data:
                        record_id = record_data.get('id')
                        if record_id:
                            success = await handler.delete(record_id)
                            results.append({"id": record_id, "deleted": success})
                    return ModelResponse.success(results)
                
                else:
                    raise HTTPException(status_code=400, detail="Invalid operation")
                    
            except HTTPException:
                raise
            except Exception as e:
                return handle_database_error(e)


# Create global server instance
server = ERPServer()
app = server.app

def create_app():
    """Create and return the FastAPI application"""
    return app
