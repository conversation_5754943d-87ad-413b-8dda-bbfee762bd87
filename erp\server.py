"""
FastAPI ASGI Server for ERP system
"""
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import H<PERSON><PERSON>esponse, RedirectResponse
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional, List
import json
import os

from .config import config
from .database.registry import DatabaseRegistry
from .addons.loader import AddonLoader
from .models.base import ModelRegistry
from .utils import (
    APIResponse, ModelResponse, handle_database_error, handle_generic_error,
    ModelRequestHandler, RequestValidator, AuthenticationHandler,
    timing_middleware, error_handling_middleware, logging_middleware, database_middleware,
    environment_middleware
)


class ERPServer:
    """Main ERP server application"""

    def __init__(self):
        self.addon_loader = AddonLoader()
        self.app = self._create_app()
        self._setup_static_files()
        self._setup_middleware()
        self._setup_routes()
    
    def _create_app(self) -> FastAPI:
        """Create FastAPI application with lifespan management"""
        
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # Startup
            print("Starting ERP server...")
            await self._load_addons()
            yield
            # Shutdown
            print("Shutting down ERP server...")
            await DatabaseRegistry.close_all()
        
        return FastAPI(
            title="ERP System",
            description="Odoo-like ERP system with async support",
            version="1.0.0",
            lifespan=lifespan
        )

    def _setup_static_files(self):
        """Setup static files and templates"""
        # Mount static files
        if os.path.exists("static"):
            self.app.mount("/static", StaticFiles(directory="static"), name="static")

        # Setup templates
        if os.path.exists("templates"):
            self.templates = Jinja2Templates(directory="templates")
        else:
            self.templates = None
    
    def _setup_middleware(self):
        """Setup FastAPI middleware"""
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=config.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Custom middleware (order matters - database_middleware must come before environment_middleware)
        self.app.middleware("http")(timing_middleware)
        self.app.middleware("http")(error_handling_middleware)
        self.app.middleware("http")(logging_middleware)
        self.app.middleware("http")(environment_middleware)
        self.app.middleware("http")(database_middleware)
    
    async def _load_addons(self):
        """Load all addons"""
        try:
            await self.addon_loader.discover_addons()
            await self.addon_loader.load_addons()
            print(f"Loaded {len(self.addon_loader.loaded_addons)} addons")
        except Exception as e:
            print(f"Error loading addons: {e}")
            raise
    
    def _setup_routes(self):
        """Setup API routes"""

        @self.app.get("/")
        async def root():
            # If list_db is enabled, redirect to database list
            if config.list_db:
                return RedirectResponse(url="/app/database/list", status_code=302)
            return APIResponse.success({"message": "ERP System API", "version": "1.0.0"})
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            try:
                # Check database connection
                db = await DatabaseRegistry.get_current_database()
                if db:
                    await db.execute("SELECT 1")
                    db_status = "connected"
                else:
                    db_status = "no_connection"
                
                return APIResponse.success({
                    "status": "healthy",
                    "database": db_status,
                    "addons": len(self.addon_loader.loaded_addons)
                })
            except Exception as e:
                return APIResponse.error(f"Health check failed: {str(e)}", status_code=503)
        
        @self.app.get("/addons")
        async def list_addons():
            """List all loaded addons"""
            addons_info = {}
            for name, manifest in self.addon_loader.loaded_addons.items():
                addons_info[name] = {
                    "name": manifest.name,
                    "version": manifest.version,
                    "description": manifest.description,
                    "depends": manifest.depends,
                    "installable": manifest.installable
                }
            return APIResponse.success(addons_info)
        
        @self.app.get("/models")
        async def list_models():
            """List all registered models"""
            models_info = {}
            for name, model_class in ModelRegistry.all().items():
                models_info[name] = {
                    "name": name,
                    "description": getattr(model_class, '_description', ''),
                    "table": getattr(model_class, '_table', ''),
                    "fields": list(getattr(model_class, '_fields', {}).keys())
                }
            return APIResponse.success(models_info)
        
        # Model CRUD endpoints
        @self.app.post("/api/{model_name}")
        async def create_record(model_name: str, request: Request):
            """Create a new record"""
            try:
                data = await request.json()
                handler = ModelRequestHandler(model_name)
                record = await handler.create(data)
                return ModelResponse.success(record)
            except Exception as e:
                return handle_database_error(e)
        
        @self.app.get("/api/{model_name}")
        async def search_records(model_name: str, request: Request):
            """Search records with domain and options"""
            try:
                # Parse query parameters
                domain = request.query_params.get('domain', '[]')
                limit = int(request.query_params.get('limit', 100))
                offset = int(request.query_params.get('offset', 0))
                order = request.query_params.get('order', 'id')
                
                # Parse domain from JSON string
                if isinstance(domain, str):
                    domain = json.loads(domain)
                
                handler = ModelRequestHandler(model_name)
                records = await handler.search(domain, limit=limit, offset=offset, order=order)
                return ModelResponse.success(records)
            except Exception as e:
                return handle_database_error(e)
        
        @self.app.get("/api/{model_name}/{record_id}")
        async def read_record(model_name: str, record_id: str):
            """Read a specific record"""
            try:
                handler = ModelRequestHandler(model_name)
                record = await handler.read(record_id)
                if record:
                    return ModelResponse.success(record)
                else:
                    raise HTTPException(status_code=404, detail="Record not found")
            except HTTPException:
                raise
            except Exception as e:
                return handle_database_error(e)
        
        @self.app.put("/api/{model_name}/{record_id}")
        async def update_record(model_name: str, record_id: str, request: Request):
            """Update a specific record"""
            try:
                data = await request.json()
                handler = ModelRequestHandler(model_name)
                record = await handler.update(record_id, data)
                if record:
                    return ModelResponse.success(record)
                else:
                    raise HTTPException(status_code=404, detail="Record not found")
            except HTTPException:
                raise
            except Exception as e:
                return handle_database_error(e)
        
        @self.app.delete("/api/{model_name}/{record_id}")
        async def delete_record(model_name: str, record_id: str):
            """Delete a specific record"""
            try:
                handler = ModelRequestHandler(model_name)
                success = await handler.delete(record_id)
                if success:
                    return ModelResponse.success({"deleted": True})
                else:
                    raise HTTPException(status_code=404, detail="Record not found")
            except HTTPException:
                raise
            except Exception as e:
                return handle_database_error(e)
        
        # Batch operations
        @self.app.post("/api/{model_name}/batch")
        async def batch_operations(model_name: str, request: Request):
            """Perform batch operations on records"""
            try:
                data = await request.json()
                operation = data.get('operation')
                records_data = data.get('records', [])
                
                handler = ModelRequestHandler(model_name)
                
                if operation == 'create':
                    results = []
                    for record_data in records_data:
                        record = await handler.create(record_data)
                        results.append(record)
                    return ModelResponse.success(results)
                
                elif operation == 'update':
                    results = []
                    for record_data in records_data:
                        record_id = record_data.pop('id', None)
                        if record_id:
                            record = await handler.update(record_id, record_data)
                            results.append(record)
                    return ModelResponse.success(results)
                
                elif operation == 'delete':
                    results = []
                    for record_data in records_data:
                        record_id = record_data.get('id')
                        if record_id:
                            success = await handler.delete(record_id)
                            results.append({"id": record_id, "deleted": success})
                    return ModelResponse.success(results)
                
                else:
                    raise HTTPException(status_code=400, detail="Invalid operation")

            except HTTPException:
                raise
            except Exception as e:
                return handle_database_error(e)

        # Database list web interface
        @self.app.get("/app/database/list", response_class=HTMLResponse)
        async def database_list_page(request: Request):
            """Database selection page"""
            if not config.list_db:
                raise HTTPException(status_code=404, detail="Database listing is disabled")

            if not self.templates:
                raise HTTPException(status_code=500, detail="Templates not configured")

            return self.templates.TemplateResponse("database_list.html", {"request": request})

        # Database API endpoints
        @self.app.get("/api/databases")
        async def list_databases_api():
            """API endpoint to list available databases"""
            if not config.list_db:
                raise HTTPException(status_code=403, detail="Database listing is disabled")

            try:
                # In single database mode, show only the configured database
                if config.is_single_db_mode:
                    db_name = config.get_default_database()
                    if db_name:
                        db_info_list = [{
                            'name': db_name,
                            'size': 'Unknown',
                            'created': None,
                            'owner': 'erp',
                            'encoding': 'UTF8',
                            'status': 'active'
                        }]
                    else:
                        db_info_list = []
                else:
                    # Multi-database mode: list all databases
                    databases = await DatabaseRegistry.list_databases()

                    # Get additional database information
                    db_info_list = []
                    for db_name in databases:
                        # Apply database filter if configured
                        if config.db_filter:
                            import re
                            if not re.match(config.db_filter, db_name):
                                continue

                        # Get database size and other info
                        try:
                            db_manager = await DatabaseRegistry.get_database('postgres')
                            size_query = f"SELECT pg_size_pretty(pg_database_size('{db_name}')) as size"
                            size_result = await db_manager.fetch(size_query)
                            size = size_result[0]['size'] if size_result else 'Unknown'

                            # Get creation date (approximate)
                            created_query = f"""
                                SELECT (pg_stat_file('base/'||oid||'/PG_VERSION')).modification as created
                                FROM pg_database WHERE datname = '{db_name}'
                            """
                            created_result = await db_manager.fetch(created_query)
                            created = created_result[0]['created'].isoformat() if created_result else None

                        except Exception as e:
                            print(f"Error getting info for database {db_name}: {e}")
                            size = 'Unknown'
                            created = None

                        db_info_list.append({
                            'name': db_name,
                            'size': size,
                            'created': created,
                            'owner': 'erp',  # Default owner
                            'encoding': 'UTF8',  # Default encoding
                            'status': 'active'  # Default status
                        })

                return APIResponse.success(db_info_list)

            except Exception as e:
                return handle_database_error(e)

        @self.app.post("/api/databases")
        async def create_database_api(request: Request):
            """API endpoint to create a new database"""
            if not config.list_db:
                raise HTTPException(status_code=403, detail="Database creation is disabled")

            try:
                data = await request.json()
                db_name = data.get('name')
                language = data.get('language', 'en_US')
                demo = data.get('demo', False)

                if not db_name:
                    raise HTTPException(status_code=400, detail="Database name is required")

                # Validate database name
                import re
                if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', db_name):
                    raise HTTPException(status_code=400, detail="Invalid database name format")

                # Apply database filter if configured
                if config.db_filter:
                    if not re.match(config.db_filter, db_name):
                        raise HTTPException(status_code=400, detail="Database name does not match filter")

                # Create database
                db_manager = await DatabaseRegistry.get_database('postgres')
                create_query = f'CREATE DATABASE "{db_name}" OWNER erp'
                await db_manager.execute(create_query)

                return APIResponse.success({
                    'name': db_name,
                    'message': 'Database created successfully'
                })

            except HTTPException:
                raise
            except Exception as e:
                return handle_database_error(e)

        @self.app.delete("/api/databases/{db_name}")
        async def delete_database_api(db_name: str):
            """API endpoint to delete a database"""
            if not config.list_db:
                raise HTTPException(status_code=403, detail="Database deletion is disabled")

            try:
                # Validate database name
                if not db_name or db_name in ['postgres', 'template0', 'template1']:
                    raise HTTPException(status_code=400, detail="Cannot delete system database")

                # Apply database filter if configured
                if config.db_filter:
                    import re
                    if not re.match(config.db_filter, db_name):
                        raise HTTPException(status_code=400, detail="Database not accessible")

                # Drop database
                db_manager = await DatabaseRegistry.get_database('postgres')

                # Terminate connections to the database first
                terminate_query = f"""
                    SELECT pg_terminate_backend(pid)
                    FROM pg_stat_activity
                    WHERE datname = '{db_name}' AND pid <> pg_backend_pid()
                """
                await db_manager.execute(terminate_query)

                # Drop the database
                drop_query = f'DROP DATABASE IF EXISTS "{db_name}"'
                await db_manager.execute(drop_query)

                return APIResponse.success({
                    'name': db_name,
                    'message': 'Database deleted successfully'
                })

            except HTTPException:
                raise
            except Exception as e:
                return handle_database_error(e)


# Create global server instance
server = ERPServer()
app = server.app

def create_app():
    """Create and return the FastAPI application"""
    return app
