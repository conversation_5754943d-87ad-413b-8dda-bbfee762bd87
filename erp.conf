[options]
# =============================================================================
# ERP SYSTEM CONFIGURATION
# =============================================================================
# This is the consolidated configuration file for the ERP system.
# Only one configuration file is needed in the root directory.

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
http_port = 8069
http_interface = 127.0.0.1
server_type = asgi

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp

# =============================================================================
# DATABASE MODE CONFIGURATION
# =============================================================================
# The system supports two modes:
#
# 1. SINGLE DATABASE MODE (Mono DB):
#    - Set db_name to a specific database name
#    - The system will only connect to this one database
#    - Example: db_name = erp_production
#
# 2. MULTI-DATABASE MODE:
#    - Leave db_name empty or comment it out
#    - The system can work with multiple databases
#    - Use db_filter to control which databases are accessible
#
# Current setting: SINGLE DATABASE MODE
db_name = erp_db

# =============================================================================
# MULTI-DATABASE SUPPORT
# =============================================================================
# These settings only apply when db_name is not set (multi-database mode)
#
# list_db: Allow listing of available databases
list_db = True

# db_filter: Filter which databases are accessible
# - If null/empty: ALL databases are loaded and accessible
# - If not null: Only databases matching the pattern are accessible
# - Uses regex patterns (e.g., ^erp_.* matches databases starting with "erp_")
#
# Examples:
# db_filter = ^erp_.*          # Only databases starting with "erp_"
# db_filter = ^(erp|test)_.*   # Databases starting with "erp_" or "test_"
# db_filter =                  # Empty = load ALL databases
db_filter = ^erp_.*

# =============================================================================
# CONNECTION POOLING
# =============================================================================
db_pool_min_size = 10
db_pool_max_size = 20

# =============================================================================
# ADDONS CONFIGURATION
# =============================================================================
# Multiple addon paths can be specified, comma-separated
# The system will search for addons in all specified paths
# If the same addon exists in multiple paths, the first one found is used
#
# Examples:
# addons_path = addons                                    # Single path
# addons_path = addons,custom_addons                      # Two paths
# addons_path = addons,/opt/erp/addons,~/my_addons       # Multiple paths
addons_path = addons

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
log_level = info
log_file = erp.log

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
admin_passwd = admin

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# CORS origins (comma-separated)
# Use * for all origins, or specify specific origins
# Example: cors_origins = http://localhost:3000,https://myapp.com
cors_origins = *