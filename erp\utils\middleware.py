"""
Common middleware utilities
"""
from fastapi import Request, HTTPException
from typing import Callable, Any
import time
import logging
from ..database.registry import DatabaseRegistry
from .responses import handle_database_error, handle_generic_error
from ..environment import EnvironmentManager
from ..context import ContextManager

logger = logging.getLogger(__name__)


async def database_middleware(request: Request, call_next: Callable) -> Any:
    """Database connection middleware"""
    from ..config import config

    # Determine database name based on configuration mode
    db_name = None

    if config.is_single_db_mode:
        # Single database mode: use configured database
        db_name = config.get_default_database()
    else:
        # Multi-database mode: extract from request
        db_name = request.headers.get('X-Database')
        if not db_name:
            # Try to get from query params
            db_name = request.query_params.get('db')

        # Validate database name against filter in multi-db mode
        if db_name:
            import re
            db_filter = config.db_filter
            if not re.match(db_filter, db_name):
                logger.warning(f"Database {db_name} does not match filter {db_filter}")
                db_name = None

    # If no database name determined, this is an error
    if not db_name:
        if config.is_single_db_mode:
            logger.error("No database configured in single database mode")
            raise ValueError("Database not configured")
        else:
            logger.error("No database specified in multi-database mode")
            raise ValueError("Database not specified")

    # Set current database
    DatabaseRegistry.set_current_database(db_name)
    request.state.db_name = db_name

    try:
        response = await call_next(request)
        return response
    except Exception as e:
        logger.error(f"Database middleware error: {e}")
        raise handle_database_error(e)


async def timing_middleware(request: Request, call_next: Callable) -> Any:
    """Request timing middleware"""
    start_time = time.perf_counter()
    
    response = await call_next(request)
    
    process_time = time.perf_counter() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


async def error_handling_middleware(request: Request, call_next: Callable) -> Any:
    """Global error handling middleware"""
    try:
        response = await call_next(request)
        return response
    except HTTPException:
        # Re-raise HTTP exceptions (they're handled by FastAPI)
        raise
    except Exception as e:
        logger.error(f"Unhandled error in {request.url}: {e}", exc_info=True)
        raise handle_generic_error(e)


async def logging_middleware(request: Request, call_next: Callable) -> Any:
    """Request logging middleware"""
    start_time = time.perf_counter()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url}")
    
    try:
        response = await call_next(request)
        
        # Log response
        process_time = time.perf_counter() - start_time
        logger.info(
            f"Response: {response.status_code} "
            f"({process_time:.3f}s) {request.method} {request.url}"
        )
        
        return response
    except Exception as e:
        process_time = time.perf_counter() - start_time
        logger.error(
            f"Error: {type(e).__name__} "
            f"({process_time:.3f}s) {request.method} {request.url}: {e}"
        )
        raise


async def environment_middleware(request: Request, call_next: Callable) -> Any:
    """Environment context middleware - sets up ERP Environment for each request"""

    # Get database name from request state (set by database_middleware)
    db_name = getattr(request.state, 'db_name', None)
    if not db_name:
        logger.warning("No database name found in request state")
        response = await call_next(request)
        return response

    # Get user ID from request (authentication should set this)
    # For now, use a default user ID (1 = admin)
    # TODO: Integrate with proper authentication system
    uid = getattr(request.state, 'user_id', None)
    if uid is None:
        # Try to get from headers or query params
        uid = request.headers.get('X-User-ID')
        if uid:
            try:
                uid = int(uid)
            except ValueError:
                uid = None

        if uid is None:
            uid_param = request.query_params.get('uid')
            if uid_param:
                try:
                    uid = int(uid_param)
                except ValueError:
                    uid = None

    # Default to admin user if no user specified
    if uid is None:
        uid = 1
        logger.debug("No user ID specified, defaulting to admin (uid=1)")

    # Extract context from request
    context = {}

    # Add language from headers
    lang = request.headers.get('Accept-Language', 'en_US')
    if lang:
        context['lang'] = lang.split(',')[0].replace('-', '_')

    # Add timezone from headers
    tz = request.headers.get('X-Timezone')
    if tz:
        context['tz'] = tz

    # Add any custom context from headers (X-Context-*)
    for header_name, header_value in request.headers.items():
        if header_name.lower().startswith('x-context-'):
            context_key = header_name[10:].lower()  # Remove 'x-context-' prefix
            context[context_key] = header_value

    try:
        # Create environment
        env = await EnvironmentManager.create_environment(db_name, uid, context)

        # Set environment in context
        async with ContextManager.with_context(env=env):
            # Store environment in request state for easy access
            request.state.env = env

            # Continue with request
            response = await call_next(request)
            return response

    except Exception as e:
        logger.error(f"Error creating environment: {e}")
        raise handle_generic_error(e)
